\documentclass[titlepage, a4paper]{article}
%\usepackage{euler}
\usepackage{graphicx, amssymb, amsmath, textcomp, booktabs}
\usepackage[libertine,vvarbb]{newtxmath}
\usepackage[scr=rsfso]{mathalfa}
% \usepackage[lining,semibold,type1]{libertine} % a bit lighter than Times--no osf in math
\usepackage[T1]{fontenc} % best for Western European languages
\usepackage{minted}
\usepackage{listings, color, setspace, titlesec, fancyhdr, mdframed, multicol}
\usepackage{ucharclasses}
\usepackage{xunicode, xltxtra}
\usepackage[inner=1.25cm, outer=0.8cm, top=1.7cm, bottom=0.0cm]{geometry}
\usepackage{pdfpages}
\usepackage{tocloft}
\usepackage{nameref}
\usepackage{verbatim}
\usepackage{relsize}
\usepackage{fontspec}
\usepackage[colorlinks, linkcolor = black]{hyperref}
\usepackage[table]{xcolor}
\usepackage{tabularx}
\usepackage{enumitem}
% configure fonts
% if not using CJK
% \newfontfamily\substitutefont{SimSun}[Scale=0.8,BoldFont=SimHei]
% \setTransitionsForChinese{\begingroup\substitutefont}{\endgroup}
\usepackage{xeCJK}
\setCJKmainfont{Source Han Serif SC}[Scale=0.8]
\setCJKmonofont{SimHei}[Scale=0.8]
\setCJKsansfont{KaiTi}[Scale=0.8]

\setmainfont{Linux Libertine O}[Scale=0.925]
\setmonofont{Consolas}[Scale=0.775]
%\setsansfont{Gill Sans Medium}

\XeTeXlinebreaklocale "zh"
\XeTeXlinebreakskip = 0pt plus 1pt

\setlength{\parindent}{0em}\setlength{\parskip}{1pt}
\setlength\itemsep{1pt}

\makeatletter
\renewcommand{\paragraph}{%
  \@startsection{paragraph}{4}%
  {\z@}{1pt \@plus 1pt \@minus 1pt}{-1em}%
  {\normalfont\normalsize\bfseries}%
}
\makeatother


%configure the top corners
\pagestyle{fancy}
\setlength{\headsep}{0.1cm}

\chead{Nemesis}
\rhead{Page \thepage}
\lhead{上海交通大学 Shanghai Jiao Tong University}

%configure space between the two columns
\setlength{\columnsep}{13pt}

%configure minted to display codes
%\definecolor{Gray}{rgb}{0.9,0.9,0.9}

%remove leading numbers in table of contents
%\setcounter{secnumdepth}{0}

%configure section style of table of content
\renewcommand\cftsecfont{\Large}

%configure section style
\titleformat{\section}
{\huge}			% The style of the section title
{\thesection.}				% a prefix
{4pt}						% How much space exists between the prefix and the title
{}					% How the section is represented
% \titleformat{\section}{\huge}{}{0pt}{}
\titlespacing{\section}{0pt}{0pt}{0pt}
\titlespacing{\subsection}{0pt}{0pt}{0pt}
\titlespacing{\subsubsection}{0pt}{0pt}{0pt}

%enable section to start new page automatically
%\let\stdsection\section
%\renewcommand\section{\penalty-100\vfilneg\stdsection}

%\renewcommand\theFancyVerbLine{\arabic{FancyVerbLine}}
\renewcommand{\theFancyVerbLine}{\sffamily \textcolor[rgb]{0.5,0.5,0.5}{\scriptsize {\arabic{FancyVerbLine}}}}

\setminted[cpp]{
	style=xcode,
	mathescape,
	linenos,
	autogobble,
	baselinestretch=0.8,
	tabsize=3,
	fontsize=\normalsize,
	%bgcolor=Gray,
	frame=single,
	framesep=1mm,
	framerule=0.3pt,
	numbersep=1mm,
	breaklines=true,
	breaksymbolsepleft=2pt,
	%breaksymbolleft=\raisebox{0.8ex}{ \small\reflectbox{\carriagereturn}}, %not moe!
	%breaksymbolright=\small\carriagereturn,
	breakbytoken=false,
	showtabs=true,
	tab={\relscale{0.6} $\big\vert \ \ \ $ \relscale{1}},
}
\setminted[java]{
	style=xcode,
	mathescape,
	linenos,
	autogobble,
	baselinestretch=0.8,
	tabsize=3,
	fontsize=\small,
	%bgcolor=Gray,
	frame=single,
	framesep=1mm,
	framerule=0.3pt,
	numbersep=1mm,
	breaklines=true,
	breaksymbolsepleft=2pt,
	%breaksymbolleft=\raisebox{0.8ex}{ \small\reflectbox{\carriagereturn}}, %not moe!
	%breaksymbolright=\small\carriagereturn,
	breakbytoken=false,
	showtabs=true,
	tab={\relscale{0.6} $\big\vert \ \ \ $ \relscale{1}},
}
\setminted[python]{
	style=xcode,
	mathescape,
	linenos,
	autogobble,
	baselinestretch=0.8,
	tabsize=3,
	fontsize=\small,
	%bgcolor=Gray,
	frame=single,
	framesep=1mm,
	framerule=0.3pt,
	numbersep=1mm,
	breaklines=true,
	breaksymbolsepleft=2pt,
	%breaksymbolleft=\raisebox{0.8ex}{ \small\reflectbox{\carriagereturn}}, %not moe!
	%breaksymbolright=\small\carriagereturn,
	breakbytoken=false,
	showtabs=true,
	tab={\relscale{0.6} $\big\vert \ \ \ $ \relscale{1}},
}

\setminted[vim]{
	style=xcode,
	mathescape,
	linenos,
	autogobble,
	baselinestretch=0.8,
	tabsize=2,
	fontsize=\normalsize,
	%bgcolor=Gray,
	frame=single,
	framesep=1mm,
	framerule=0.3pt,
	numbersep=1mm,
	breaksymbolsepleft=2pt,
	%breaksymbolleft=\raisebox{0.8ex}{ \small\reflectbox{\carriagereturn}}, %not moe!
	%breaksymbolright=\small\carriagereturn,
	breakbytoken=false,
}

\setminted[sh]{
	style=xcode,
	mathescape,
	linenos,
	autogobble,
	baselinestretch=0.8,
	tabsize=2,
	fontsize=\normalsize,
	%bgcolor=Gray,
	frame=single,
	framesep=1mm,
	framerule=0.3pt,
	numbersep=1mm,
	breaklines=true,
	breaksymbolsepleft=2pt,
	%breaksymbolleft=\raisebox{0.8ex}{ \small\reflectbox{\carriagereturn}}, %not moe!
	%breaksymbolright=\small\carriagereturn,
	breakbytoken=false,
}


%THE SCL BEGINS
\begin{document}
	\begin{titlepage}
		\input{cover/cover.tex}
	\end{titlepage}
	\begin{multicols}{2}
		\setcounter{tocdepth}{3}
		\begingroup
		\let\cleardoublepage\relax
		\let\clearpage\relax
		\begin{small}
		\begin{spacing}{0.70}
		\tableofcontents
		\end{spacing}
		\end{small}
		\newpage
		\begin{spacing}{0.6}

			\section{Geometry}
				\subsection{二维几何基础操作}
				%\subsection{凸包}
					\inputminted{cpp}{src/Geometry/凸包.cpp}
					\inputminted{cpp}{src/Geometry/闵可夫斯基和.cpp}
					\inputminted{cpp}{src/Geometry/geo.cpp}
				%	\inputminted{cpp}{src/Geometry/far_pair.cpp}
				% \subsection{直线半平面交}
					\inputminted{cpp}{src/Geometry/半平面交.cpp}
				\subsection{整数半平面交}
					\inputminted{cpp}{src/Geometry/integral_hpi.cpp}
				\subsection{凸包询问：凸包内、切点、交点、最近点}
					\inputminted{cpp}{src/Geometry/convex_findmax.cpp}



				 \subsection{点、线段在简单多边形内}
					\inputminted{cpp}{src/Geometry/AirportConstruction.cpp}
				\subsection{$O(n ^ 2)$ Ear Clipping 三角剖分}
				\inputminted{cpp}{src/Geometry/Triangulation.cpp}
				\subsection{单插入动态凸包}
				\inputminted{cpp}{src/Geometry/dynamic_convex_hull.cpp}
				\input
					% \subsection{凸包快速询问}
					% \inputminted{cpp}{src/Geometry/logconvexhull.cpp}
%				\subsection{直线与凸包交点}
%				\inputminted{cpp}{src/Geometry/直线与凸包交点.cpp}
%				\subsection{点到凸包切线}
%				\inputminted{cpp}{src/Geometry/点到凸包切线.cpp}
				% \subsection{Delaunay 三角剖分}
				% \inputminted{cpp}{src/Geometry/DelaunayTriangulation.cpp}

				\subsection{圆}
					\inputminted[highlightlines={7}]{cpp}{src/Geometry/circle.cpp}
				\subsection{圆反演，阿波罗尼茨圆}
				\begin{small}
					\input{src/Geometry/阿波罗尼茨圆.tex}
				\end{small}
					\inputminted{cpp}{src/yzh/circle_inversion.cpp}
				\subsection{圆并}
					\inputminted{cpp}{src/Geometry/圆并.cpp}
				\subsection{多边形与圆交}
					\inputminted{cpp}{src/Geometry/多边形和圆的交.cpp}

				%\subsection{圆幂~圆反演~根轴}
				%	\input{src/Geometry/圆幂.tex}
				\subsection{球面基础, 经纬度球面距离}
				\begin{small}
					\input{src/Geometry/球面.tex}
				\end{small}
					\inputminted{cpp}{src/Geometry/经纬度求球面最短距离.cpp}
%				\subsection{最近点对}
%				\inputminted{cpp}{src/Geometry/最近点对.cpp}
				%\subsection{长方体表面两点最短距离}
				%\inputminted{cpp}{src/Geometry/长方体表面两点最短距离.cpp}
				\subsection{圆上整点}
					\inputminted{cpp}{src/Geometry/圆上整点.cpp}
				\subsection{三相之力}
				\inputminted{cpp}{src/Geometry/三角形.cpp}
				\subsection{相关公式}
					\input{src/Geometry/Formula(Saga).tex}
				\subsection{三维几何基础操作}
					\inputminted{cpp}{src/Geometry/三维几何.cpp}
				\subsection{三维凸包}
					\inputminted{cpp}{src/Geometry/三维凸包.cpp}
				\subsection{最小覆盖球}
					\inputminted{cpp}{src/Geometry/最小覆盖球.cpp}
				%	\newpage

			\section{Tree \& Graph}
				\subsection{Hopcroft-Karp $O(\sqrt{V} E)$}
					\inputminted[highlightlines={5,9}]{cpp}{src/TreeandGraph/hk_skip2004.cpp}
				\subsection{Hungarian $O(V E / w)$}
					\inputminted{cpp}{src/TreeandGraph/Hungarian.cpp}
				\subsection{Shuffle 一般图最大匹配 $O(V E)$}
					\inputminted{cpp}{src/TreeandGraph/一般图最大匹配-shuffle.cpp}
				\subsection{极大团计数}
					\inputminted{cpp}{src/TreeandGraph/CliqueCount.cpp}
				% \subsection{有根树同构 Hash}
				% 	可重集合 Hash, 一个子树要换一种 Hash 做打包.
				% 	通常 sort rolling hash, 然后 xorshift 打包.
				% 	如果使用 xor hash, 注意相同子树贡献不应该抵消.
				\subsection{KM 最大权匹配 $O(V^3)$}
					\inputminted{cpp}{src/TreeandGraph/KM.cpp}
				\subsection{欧拉回路}
				\inputminted{cpp}{src/TreeandGraph/欧拉回路.cpp}
				%\subsection{Blossom 一般图带权最大匹配 $O(|V|^3)$}
				%\inputminted{cpp}{src/TreeandGraph/Blossom.cpp}
				\subsection{2-SAT, 强连通分量 / Bitset Kosaraju}
					\inputminted{cpp}{src/TreeandGraph/2-sat.cpp}
					\inputminted{cpp}{src/TreeandGraph/kosaraju.cpp}
				\subsection{Tarjan 点双，边双}
					\inputminted{cpp}{src/TreeandGraph/Tarjan.cpp}
				\subsection{Dominator Tree 支配树}
					\inputminted{cpp}{src/TreeandGraph/支配树.cpp}
				%\subsection{最大团}
				%\inputminted{cpp}{src/TreeandGraph/MaximumClique.cpp}
				\subsection{Dinic 最大流}
					\input{src/yzh/dinic_complexity.tex}
					\inputminted[highlightlines={8,30}]{cpp}{src/TreeandGraph/Dinic.cpp}
				\subsection{原始对偶费用流}
					\inputminted[highlightlines={2,3,22,25,28,31,42,43}]{cpp}{src/TreeandGraph/多路增广费用流.cpp}
				%\subsection{K短路}
				%\inputminted{cpp}{src/TreeandGraph/K短路.cpp}
				%\subsection{动态 MST}
				%\inputminted{cpp}{src/TreeandGraph/完全动态MST.cpp}
				%\subsection{三元环}
				%\inputminted{cpp}{src/TreeandGraph/三元环.cpp}
				%\subsection{平面图转对偶图}
				%\inputminted{cpp}{src/TreeandGraph/平面图转对偶图.cpp}
				%\subsection{最小乘积生成树}
				%\inputminted{cpp}{src/TreeandGraph/最小乘积生成树.cpp}
				%\subsection{树同构}
				%\inputminted{cpp}{src/TreeandGraph/树同构.cpp}
				\subsection{虚树}
				\inputminted{cpp}{src/TreeandGraph/虚树.cpp}
				%\subsection{动态最小生成树}
				%\inputminted{cpp}{src/TreeandGraph/DynamicMST.cpp}
				\input{src/tbr/网络流总结.tex}
				\subsection{Gomory-Hu 无向图最小割树\,$O(V ^ 3 E)$}
					\input{src/yzh/Gomory-Hu.tex}
				\subsection{Stoer-Wagner\,无向图最小割\,$O(VE + V ^ 2 \log V)$}
					\inputminted{cpp}{src/yzh/Stoer-Wagner.cpp}
				\subsection{弦图}
					\input{src/yzh/chordal-graph.tex}
					\inputminted{cpp}{src/TreeandGraph/弦图.cpp}
				\subsection{Minimum Mean Cycle 最小平均值环 $O(n^2)$}
					\inputminted{cpp}{src/TreeandGraph/MeanCycle.cpp}
				\subsection{一般图最大匹配 - Blossom}
					\inputminted{cpp}{src/TreeandGraph/Blossom.cpp}
				% \subsection{斯坦纳树}
				% 	\inputminted{cpp}{src/TreeandGraph/斯坦纳树.cpp}
				%\subsection{Cactus 仙人掌}
					%\inputminted{cpp}{src/TreeandGraph/cactus.cpp}
				\subsection{图论结论}
					\input{src/tbr/最小乘积问题.tex}
					\input{src/tbr/最小环问题.tex}
					\input{src/tbr/度序列的可图性.tex}
					\input{src/tbr/切比雪夫距离与曼哈顿距离互化.tex}
					%\subsection{图论知识 - Nightfall}
					\input{src/TreeandGraph/图论知识.tex}
					\input{src/tbr/拟阵交问题.tex}
					\subsubsection{双极定向}
					\inputminted{cpp}{src/tbr/bipolar_orientation.cpp}
					\input{src/TreeandGraph/shanghai19.tex}

			\section{Data Structure}
				\subsection{非递归线段树}
					% \subsubsection{区间加，区间求和}
					% 	\inputminted{cpp}{src/DataStructure/非递归线段树求和.cpp}
					\subsubsection{区间加，区间求最大值}
						\inputminted{cpp}{src/DataStructure/非递归线段树求最大值.cpp}
				\subsection{点分治}
					\inputminted{cpp}{src/DataStructure/点分治.cpp}
				% \subsection{KD 树}
				% 	\inputminted{cpp}{src/DataStructure/KD tree.cpp}
				\subsection{LCT 动态树}
					\inputminted{cpp}{src/DataStructure/LCT.cpp}
				\subsection{可持久化 Treap}
					\inputminted{cpp}{src/DataStructure/可持久化treap-zmc.cpp}
				% \subsection{有旋 Treap}
				% 	\inputminted{cpp}{src/DataStructure/有旋treap.cpp}

			\section{String}
				\subsection{最小表示法}
					\inputminted{cpp}{src/String/最小表示法.cpp}
				\subsection{Manacher}
					\inputminted{cpp}{src/String/Manacher.cpp}
				\subsection{KMP, exKMP}
					\inputminted{cpp}{src/String/KMP.cpp}
				% \subsection{exKMP (zjj)}
				% 	\inputminted{cpp}{src/zjj/exkmp.cpp}
				\subsection{AC 自动机}
					注意代码是以 0 为根的，如果要 1-base 的话要改一下没有儿子时的逻辑。
					\inputminted{cpp}{src/String/AC 自动机.cpp}
				\subsection{Lydon Word Decomposition}
					\inputminted{cpp}{src/String/Lyndon Word.cpp}
				\subsection{后缀自动机}
					\inputminted{cpp}{src/String/SAM.cpp}
				\subsection{SAMSA \& 后缀树}
					\inputminted{cpp}{src/String/SAMSA.cpp}
				\subsection{后缀数组}
					\inputminted{cpp}{src/String/SA.cpp}
				\subsection{Suffix Balanced Tree 后缀平衡树}
					\inputminted{cpp}{src/String/后缀平衡树.cpp}
				%\subsection{Total LCS 子串对目标求 LCS}
				%\inputminted{cpp}{src/String/Total LCS.cpp}
				\subsection{广义在线 SAM}
				\inputminted{cpp}{src/String/generalizedSAM.cpp}
				\subsection{回文树}
					0 的子树是长为偶数的串, 1 的子树是长为奇数的. 0 代表空串, 1 没有意义.
					\inputminted{cpp}{src/String/PAM.cpp}
				\subsection{Runs}
					\inputminted{cpp}{src/String/Runs.cpp}
				%\subsection{双端插入回文树 (zjj)}
				%\inputminted{cpp}{src/String/PAM_bidirectional.cpp}
				\subsection{字符串 Hash}
				\texttt{\input{|"python src/Miscellany/randomprimes.py 3e2 1e6 1e12 1e13 2e13 1e15 1e17 5e17 1e18 2e18"}}
				\inputminted{cpp}{src/String/hash.cpp}
				\subsection{String Conclusions}
					\input{src/tbr/string-Conclusions.tex}
			\newpage
			\section{Math 数学}
				\subsection{Long Long $O(1)$ 乘, Barrett}
					\inputminted{cpp}{src/Miscellany/LLFPM.cpp}
				\subsection{exgcd, 逆元}
					\input{src/Math/exgcd.tex}
					\inputminted{cpp}{src/Math/exgcd.cpp}
					递推逆元: $\operatorname{inv}(i) \equiv (P - P / i) \cdot \operatorname{inv}(P \bmod i)$
				\subsection{CRT 中国剩余定理}
					\inputminted{cpp}{src/Math/CRT_lbn.cpp}
				\subsection{Miller Rabin, Pollard Rho}
					\inputminted{cpp}{src/Math/Miller Rabin And Pollard Rho.cpp}
				\subsection{扩展卢卡斯}
					\inputminted{cpp}{src/Math/扩展卢卡斯.cpp}
				\subsection{阶乘取模}
					\inputminted{cpp}{src/Math/Factorial Mod.cpp}
				\subsection{类欧几里得 直线下格点统计}
					\inputminted{cpp}{src/Math/直线下格点统计.cpp}
				\subsection{万能欧几里德}
					\inputminted{cpp}{src/zjj/euclid.cpp}
				\subsection{平方剩余}
					\inputminted{cpp}{src/Math/平方剩余.cpp}
				% \subsection{BSGS 离散对数}
				% \inputminted{cpp}{src/Math/BSGS.cpp}
				\subsection{线性同余不等式}
					\inputminted{cpp}{src/Math/线性同余不等式.cpp}
				% \subsection{min25 筛}
				% \inputminted{cpp}{src/Math/TEES.cpp}
				\subsection{原根}
				% \inputminted{cpp}{src/Math/原根.cpp
					\input{src/tbr/阶和原根.tex}
				\subsection{FFT}
					\inputminted{cpp}{src/Math/FFT.cpp}
				\subsection{NTT}
					\inputminted{cpp}{src/Math/NTT.cpp}
				\subsection{MTT 任意模数卷积}
					\inputminted{cpp}{src/Math/MTT.cpp}
				\subsection{多项式运算}
					\subsubsection{多项式求逆\ 开根\ ln exp}
						\inputminted{cpp}{src/Math/多项式运算.cpp}
					\subsubsection{多项式除法\ 取模}
						需要抄求逆。
						\inputminted{cpp}{src/Math/多项式取模.cpp}
					\subsubsection{多点求值}
						需要抄取模。
						\inputminted{cpp}{src/Math/多点求值.cpp}
					\subsubsection{插值}
						\input{src/Math/插值.tex}
					% \subsubsection*{快速插值}
					% 	\input{src/Math/快速插值.tex}
				\subsection{线性递推}
					\subsubsection*{$O(k^2 \log n)$}
						\inputminted{cpp}{src/Math/线性递推.cpp}
					\subsubsection*{$O(k \log k \log n)$ - Bostan-Mori}
						\inputminted{cpp}{src/Math/快速线性递推-bostan-mori.cpp}
					\subsubsection*{$O(k \log k \log n)$ - 多项式取模}
						需要抄前面的多项式取模。预处理 $O(k \log k \log n)$，固定 $n$ 和系数只改变初始值的话，询问一次 $O(k)$。注意只询问一次的话不如 Bostan-Mori 快。
						\inputminted{cpp}{src/Math/快速线性递推-多项式取模.cpp}
				\subsection{Berlekamp-Massey 最小多项式}
					\input{src/Math/Berlekamp-Massey.tex}
				\subsection{FWT}
					\inputminted{cpp}{src/Math/FWT.cpp}
				% \subsection{FFT NTT FWT 多项式操作}
				% 	\inputminted{cpp}{src/Math/FFT NTT FWT.cpp}
				\subsection{K 进制 FWT}
					\inputminted{cpp}{src/tbr/fwt.cpp}
				% \input{src/Math/多项式牛顿法.tex}
				% \inputminted{cpp}{src/Math/Polynomial.cpp}
					% \input{src/Math/单位根反演.tex}
				%\input{src/Math/多点求值与快速插值.tex}
				%\subsection{多项式插值}
				%\input{cpp}{src/Math/插值.tex}
				\subsection{Simplex 单纯形}
					\inputminted{cpp}{src/Math/Simplex.cpp}
				\subsection{高斯消元最小范数解}
					\inputminted{cpp}{src/Geometry/minnorm_gauss.cpp}
				%\newpage
				\subsection{Pell 方程}
					\inputminted{cpp}{src/Math/Pell方程.cpp}
				\subsection{解一元三次方程}
					\inputminted{cpp}{src/Math/解一元三次方程.cpp}
				\subsection{自适应 Simpson}
					\inputminted{cpp}{src/Math/Simpson.cpp}
				%\subsection{Schreier-Sims 求排列群的阶}
				%\inputminted{cpp}{src/Math/Schreier-Sims.cpp}


			\section{Appendix}
				\begin{spacing}{0.4}
				\subsection{Formulas 公式表}
					\input{src/tbr/appendix-Formula.tex}
				\subsection{Calculus, Integration Table 导数积分表}
					\input{src/TipsandHints/CalculusTable.tex}
					\input{src/TipsandHints/IntegrationTable_NewMeta.tex}
			%	\subsection{Java Template}
			%		\inputminted{java}{src/Miscellany/Main.java}
				\subsection{Python Hint}
					\inputminted{python}{src/Miscellany/py.py}
				\end{spacing}

			\section{Miscellany}
				\subsection{Zeller 日期公式}
					\inputminted{cpp}{src/Miscellany/日期公式.cpp}
				\subsection{有理数二分: Stern-Brocot 树, Farey 序列}
					\inputminted{python}{src/yzh/farey.py}
				\subsection{黄金三分}
					\inputminted[highlightlines={7}]{cpp}{src/yzh/golden_ternary.cpp}
				\subsection{DP 优化}
				\subsubsection{四边形不等式}
					\inputminted{cpp}{src/Miscellany/2D1D.cpp}
					\input{src/tbr/树形背包优化.tex}
				%\subsection{Dancing Links 精确覆盖搜索}
				%\inputminted{cpp}{src/Miscellany/DLX.cpp}
				\subsubsection{$O(n \cdot \max a_i)$ Subset Sum}
					\inputminted{cpp}{src/yzh/Subset Sum.cpp}

				\newpage
				\subsection{Hash Table}
					\inputminted[highlightlines={4,15,17,18}]{cpp}{src/DataStructure/hashmap.cpp}
				\subsection{基数排序}
					\inputminted{cpp}{src/Miscellany/RadixSort.cpp}
				\subsection{Hacks: O3, 读入优化, Bitset, builtin}
					\inputminted{cpp}{src/Miscellany/hack.cpp}
				\subsection{试机赛与纪律文件}
				\begin{spacing}{0.3}
					\input{src/TipsandHints/discipline.tex}
				\end{spacing}
				\subsection{Constant Table 常数表}
				\label{randomprimes}
				\begin{small}
					\setlength{\parindent}{0em}\setlength{\parskip}{0em}\renewcommand{\baselinestretch}{.8}
					\texttt{\input{|"python src/Miscellany/randomprimes.py 500 1e3 3e4 1e5 3e5 1e6 2e6 5e6 1e7 2e7 1e9 2e9"}}
					\texttt{\textbf{NTT} 976224257 r=3 (20)
					985661441 r=3 (22)
					998244353 r=3 (23)\\
					1004535809 r=3 (21)
					1007681537 r=3 (20)
					1012924417 r=5 (21)\\
					1045430273 r=3 (20)
					1051721729 r=6 (20)
					1053818881 r=7 (20)
					}
				\end{small}
				\input{src/TipsandHints/Addon.tex}
				\subsection*{Vimrc, Bashrc}
				\inputminted{vim}{src/Miscellany/vimrc}
				\inputminted{sh}{src/Miscellany/bashrc}
				VSCode: 打开自动保存
		% insert Miscellany/Grid_Paper.pdf
		% \newpage
		%	\subsection*{Delaunay 三角剖分}
		%	\inputminted[fontsize=\small]{cpp}{src/Geometry/DelaunayTriangulation.cpp}
		%	\subsection*{动态凸包}
		%	\inputminted[fontsize=\small]{cpp}{src/Geometry/dynamic_convex_hull.cpp}
		% \begin{comment}
			% \subsection*{Java Example}
				% \inputminted{java}{src/Miscellany/Main2.java}
			% \subsection*{Python Example}
			% 	\inputminted{python}{src/Miscellany/test.py}
			% \subsection*{Blossom}
			% 	\inputminted[fontsize=\small]{cpp}{src/TreeandGraph/Blossom.cpp}
			% \subsection*{Chu-liu}
			% 	\inputminted[fontsize=\small]{cpp}{src/TreeandGraph/最小树形图.cpp}
			% \subsection*{可持久化 Treap}
			% 	\inputminted[fontsize=\small]{cpp}{src/DataStructure/可持久化treap-zmc.cpp}
			% 	\subsection*{天动万象}
			%	\inputminted[fontsize=\small]{cpp}{src/yzh/ByteCampA3.cpp}
			% \subsection*{Clique Counting}
			% \inputminted[fontsize=\small]{cpp}{src/yzh/clique.cpp}
			% \subsection*{神谕}
			% 	\inputminted[fontsize=\small]{cpp}{src/zjj/zjj.cpp}
			% 	\input{src/zjj/sam.tex}

		%\end{comment}
		\end{spacing}
		\endgroup
	\end{multicols}
	\newpage
	\includegraphics{src/Miscellany/Grid_Paper.pdf}

	\begin{multicols}{2}
		\begin{spacing}{0.6}
		\begin{small}
	\input{src/Math/stl_numeric.tex}
		\end{small}
	\end{spacing}
	\end{multicols}
	% \begin{center}
	% 	\LARGE{Good Luck \&\& Have Fun!}
	% \end{center}

\end{document}
%THE SCL ENDS
